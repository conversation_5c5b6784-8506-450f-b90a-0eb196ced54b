{"name": "client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@welldone-software/why-did-you-render": "^7.0.1", "axios": "^1.2.2", "bootstrap": "^5.2.3", "lottie-react": "^2.3.1", "moment": "^2.29.4", "react": "^18.2.0", "react-bootstrap": "^2.7.0", "react-dom": "^18.2.0", "react-icons": "^4.7.1", "react-loading-skeleton": "^3.1.0", "react-redux": "^8.0.5", "react-router-dom": "^6.6.2", "react-select": "^5.7.0", "reactstrap": "^9.1.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "test": "echo \"Tests need to be configured for Vite (e.g., with V<PERSON><PERSON>)\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "vite": "^5.2.10"}}