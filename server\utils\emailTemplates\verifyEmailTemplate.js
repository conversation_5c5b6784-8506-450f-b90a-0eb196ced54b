/**
 * Modern Email Verification Template
 * Responsive and accessible email verification template
 */

const { getBaseTemplate } = require('./modernEmailTemplate');

const verifyEmailTemplate = (user, token, options = {}) => {
  const {
    appName = 'ONetwork Forum',
    appUrl = process.env.REACT_APP_URL || 'http://localhost:3001',
    primaryColor = '#007bff'
  } = options;

  const verificationUrl = `${appUrl}/verify-email?token=${token}`;
  const firstName = user.firstName || user.username || 'there';

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="${primaryColor}" stroke-width="2"/>
        <path d="m9 12 2 2 4-4" stroke="${primaryColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>

    <h2 style="color: #333; font-size: 24px; font-weight: 600; margin-bottom: 20px; text-align: center;">
      Welcome to ${appName}!
    </h2>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      Hi ${firstName},
    </p>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      Thank you for signing up for ${appName}! To complete your registration and start participating in our community, 
      please verify your email address by clicking the button below.
    </p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="${verificationUrl}" 
         class="button"
         style="display: inline-block; padding: 16px 32px; background-color: ${primaryColor}; color: #ffffff !important; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">
        Verify Email Address
      </a>
    </div>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      This verification link will expire in <strong>20 minutes</strong> for security reasons.
    </p>

    <div class="security-notice">
      <p style="margin: 0; font-size: 14px;">
        <strong>Security Notice:</strong> If you didn't create an account with ${appName}, 
        please ignore this email. Your email address will not be added to our system.
      </p>
    </div>

    <p style="font-size: 14px; line-height: 1.6; margin-top: 30px; color: #6c757d;">
      If the button above doesn't work, you can copy and paste this link into your browser:
    </p>
    
    <p style="font-size: 14px; word-break: break-all; background-color: #f8f9fa; padding: 12px; border-radius: 4px; margin: 10px 0;">
      <a href="${verificationUrl}" style="color: ${primaryColor};">${verificationUrl}</a>
    </p>

    <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

    <h3 style="color: #333; font-size: 18px; font-weight: 600; margin-bottom: 15px;">
      What's Next?
    </h3>

    <ul style="padding-left: 20px; margin-bottom: 20px;">
      <li style="margin-bottom: 8px;">Complete your profile to connect with other members</li>
      <li style="margin-bottom: 8px;">Start or join discussions on topics you're passionate about</li>
      <li style="margin-bottom: 8px;">Follow other users and build your network</li>
      <li style="margin-bottom: 8px;">Share your knowledge and help others in the community</li>
    </ul>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      We're excited to have you as part of our community!
    </p>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      Best regards,<br>
      The ${appName} Team
    </p>
  `;

  const templateOptions = {
    title: `Verify your email - ${appName}`,
    preheader: `Welcome to ${appName}! Please verify your email address to complete your registration.`,
    primaryColor,
    appName,
    appUrl
  };

  return getBaseTemplate(content, templateOptions);
};

module.exports = verifyEmailTemplate;
