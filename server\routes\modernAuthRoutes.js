/**
 * Modern Authentication Routes
 * Enhanced with rate limiting, validation, and security features
 */

const express = require("express");
const router = express.Router();
const modernAuthController = require("../controllers/modernAuthController");
const validateAccessToken = require("../middlewares/validateAccessToken");
const validateEmailVerifyToken = require("../middlewares/validateEmailVerifyToken");
const validatePasswordResetToken = require("../middlewares/validatePasswordResetToken");
const { body, validationResult } = require("express-validator");

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(422).json({
      message: "Validation failed",
      errors: errors.array()
    });
  }
  next();
};

// Registration validation rules
const registerValidation = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
  
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    })
];

// Login validation rules
const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Email validation rules
const emailValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

// Password reset validation rules
const passwordResetValidation = [
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('confirmNewPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    })
];

// Routes
router.post("/register", 
  registerValidation, 
  handleValidationErrors, 
  modernAuthController.register
);

router.post("/login", 
  loginValidation, 
  handleValidationErrors, 
  modernAuthController.login
);

router.post("/send-email-verification", 
  emailValidation, 
  handleValidationErrors, 
  modernAuthController.sendEmailVerification
);

router.post("/verify-email", 
  validateEmailVerifyToken, 
  modernAuthController.emailVerify
);

router.post("/forgot-password", 
  emailValidation, 
  handleValidationErrors, 
  modernAuthController.sendForgotPassword
);

router.post("/reset-password", 
  passwordResetValidation, 
  handleValidationErrors, 
  validatePasswordResetToken, 
  modernAuthController.resetPassword
);

// Health check endpoint for email service
router.get("/email-health", async (req, res) => {
  try {
    const emailService = require("../services/emailService");
    const isHealthy = await emailService.testConnection();
    
    res.status(200).json({
      status: "ok",
      emailService: isHealthy ? "connected" : "disconnected",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Email service health check failed",
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
