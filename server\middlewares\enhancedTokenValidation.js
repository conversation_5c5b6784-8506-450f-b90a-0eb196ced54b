/**
 * Enhanced Token Validation Middleware
 * Improved security, error handling, and token management
 */

const jwt = require("jsonwebtoken");
const User = require("../models/userModel");

/**
 * Enhanced email verification token validation
 */
const validateEmailVerifyToken = async (req, res, next) => {
  try {
    // Support both body and query parameters for token
    const token = req.body.token || req.query.token;

    if (!token) {
      return res.status(400).json({
        message: "Email verification token is required.",
        code: "TOKEN_MISSING"
      });
    }

    if (!process.env.EMAIL_VERIFY_TOKEN_SECRET_KEY) {
      console.error("EMAIL_VERIFY_TOKEN_SECRET_KEY is not configured");
      return res.status(500).json({
        message: "Server configuration error.",
        code: "SERVER_CONFIG_ERROR"
      });
    }

    try {
      const decoded = jwt.verify(token, process.env.EMAIL_VERIFY_TOKEN_SECRET_KEY);
      
      if (!decoded.email) {
        return res.status(400).json({
          message: "Invalid token format.",
          code: "INVALID_TOKEN_FORMAT"
        });
      }

      // Check if user exists
      const user = await User.findOne({ email: decoded.email.toLowerCase() });
      if (!user) {
        return res.status(404).json({
          message: "User not found.",
          code: "USER_NOT_FOUND"
        });
      }

      // Check if already verified
      if (user.isVerified) {
        return res.status(400).json({
          message: "Email is already verified.",
          code: "ALREADY_VERIFIED"
        });
      }

      req.user = { email: decoded.email };
      next();

    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(400).json({
          message: "Email verification token has expired. Please request a new verification email.",
          code: "TOKEN_EXPIRED"
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(400).json({
          message: "Invalid email verification token.",
          code: "INVALID_TOKEN"
        });
      } else {
        throw jwtError;
      }
    }

  } catch (error) {
    console.error("Email verification token validation error:", error);
    return res.status(500).json({
      message: "An error occurred during token validation.",
      code: "VALIDATION_ERROR"
    });
  }
};

/**
 * Enhanced password reset token validation
 */
const validatePasswordResetToken = async (req, res, next) => {
  try {
    // Support both body and query parameters for token
    const token = req.body.token || req.query.token;

    if (!token) {
      return res.status(400).json({
        message: "Password reset token is required.",
        code: "TOKEN_MISSING"
      });
    }

    if (!process.env.RESET_PASSWORD_TOKEN_SECRET_KEY) {
      console.error("RESET_PASSWORD_TOKEN_SECRET_KEY is not configured");
      return res.status(500).json({
        message: "Server configuration error.",
        code: "SERVER_CONFIG_ERROR"
      });
    }

    try {
      const decoded = jwt.verify(token, process.env.RESET_PASSWORD_TOKEN_SECRET_KEY);
      
      if (!decoded.email) {
        return res.status(400).json({
          message: "Invalid token format.",
          code: "INVALID_TOKEN_FORMAT"
        });
      }

      // Check if user exists
      const user = await User.findOne({ email: decoded.email.toLowerCase() });
      if (!user) {
        return res.status(404).json({
          message: "User not found.",
          code: "USER_NOT_FOUND"
        });
      }

      // Check if user is verified (optional security check)
      if (!user.isVerified) {
        return res.status(400).json({
          message: "Please verify your email address first.",
          code: "EMAIL_NOT_VERIFIED"
        });
      }

      req.user = { email: decoded.email };
      next();

    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(400).json({
          message: "Password reset token has expired. Please request a new password reset.",
          code: "TOKEN_EXPIRED"
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(400).json({
          message: "Invalid password reset token.",
          code: "INVALID_TOKEN"
        });
      } else {
        throw jwtError;
      }
    }

  } catch (error) {
    console.error("Password reset token validation error:", error);
    return res.status(500).json({
      message: "An error occurred during token validation.",
      code: "VALIDATION_ERROR"
    });
  }
};

/**
 * Enhanced access token validation
 */
const validateAccessToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      return res.status(401).json({
        message: "Access token is required.",
        code: "TOKEN_MISSING"
      });
    }

    if (!process.env.ACCESS_TOKEN_SECRET_KEY) {
      console.error("ACCESS_TOKEN_SECRET_KEY is not configured");
      return res.status(500).json({
        message: "Server configuration error.",
        code: "SERVER_CONFIG_ERROR"
      });
    }

    try {
      const decoded = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET_KEY);
      
      if (!decoded.id) {
        return res.status(401).json({
          message: "Invalid token format.",
          code: "INVALID_TOKEN_FORMAT"
        });
      }

      // Check if user still exists
      const user = await User.findById(decoded.id);
      if (!user) {
        return res.status(401).json({
          message: "User not found.",
          code: "USER_NOT_FOUND"
        });
      }

      // Check if user is still verified
      if (!user.isVerified) {
        return res.status(401).json({
          message: "Account is not verified.",
          code: "ACCOUNT_NOT_VERIFIED"
        });
      }

      req.user = {
        id: user._id,
        email: user.email,
        username: user.username,
        isVerified: user.isVerified
      };
      next();

    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          message: "Access token has expired.",
          code: "TOKEN_EXPIRED"
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          message: "Invalid access token.",
          code: "INVALID_TOKEN"
        });
      } else {
        throw jwtError;
      }
    }

  } catch (error) {
    console.error("Access token validation error:", error);
    return res.status(500).json({
      message: "An error occurred during token validation.",
      code: "VALIDATION_ERROR"
    });
  }
};

module.exports = {
  validateEmailVerifyToken,
  validatePasswordResetToken,
  validateAccessToken
};
