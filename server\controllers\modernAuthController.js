/**
 * Modern Authentication Controller
 * Enhanced with security features, rate limiting, and better error handling
 */

const User = require("../models/userModel");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { RateLimiterMemory } = require("rate-limiter-flexible");
const emailService = require("../services/emailService");
const verifyEmailTemplate = require("../utils/emailTemplates/verifyEmailTemplate");
const resetPasswordTemplate = require("../utils/emailTemplates/resetPasswordTemplate");
const generateEmailVerifyToken = require("../middlewares/generateEmailVerifyToken");
const generatePasswordResetToken = require("../middlewares/generatePasswordResetToken");
const {
  generateAccessToken,
  generateRefreshToken,
} = require("../middlewares/generateTokens");

// Rate limiters
const loginLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: 5, // Number of attempts
  duration: 900, // Per 15 minutes
  blockDuration: 900, // Block for 15 minutes
});

const registerLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: 3, // Number of registrations
  duration: 3600, // Per hour
});

const passwordResetLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.body.email || req.ip,
  points: 3, // Number of reset attempts
  duration: 3600, // Per hour
});

/**
 * Enhanced user registration with modern security features
 */
const register = async (req, res) => {
  try {
    // Rate limiting
    try {
      await registerLimiter.consume(req.ip);
    } catch (rateLimiterRes) {
      return res.status(429).json({
        message: "Too many registration attempts. Please try again later.",
        retryAfter: Math.round(rateLimiterRes.msBeforeNext / 1000)
      });
    }

    const { firstName, lastName, email, username, password, confirmPassword } = req.body;

    // Enhanced validation
    if (!firstName?.trim() || !lastName?.trim() || !email?.trim() || !username?.trim() || !password?.trim()) {
      return res.status(422).json({
        message: "All fields are required!",
        fields: {
          firstName: !firstName?.trim(),
          lastName: !lastName?.trim(),
          email: !email?.trim(),
          username: !username?.trim(),
          password: !password?.trim()
        }
      });
    }

    // Password confirmation check
    if (password !== confirmPassword) {
      return res.status(422).json({
        message: "Passwords do not match!"
      });
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(422).json({
        message: "Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character."
      });
    }

    // Validate email format
    try {
      await emailService.validateEmail(email);
    } catch (error) {
      return res.status(422).json({
        message: error.message
      });
    }

    // Check for existing users
    const existingUser = await User.findOne({
      $or: [{ email: email.toLowerCase() }, { username: username.toLowerCase() }]
    });

    if (existingUser) {
      if (existingUser.email === email.toLowerCase()) {
        return res.status(400).json({
          message: "An account already exists with this email!"
        });
      } else {
        return res.status(400).json({
          message: "An account already exists with this username!"
        });
      }
    }

    // Create user
    const hashedPassword = await bcrypt.hash(password, 12); // Increased salt rounds
    const user = await User.create({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      username: username.toLowerCase().trim(),
      password: hashedPassword,
      isVerified: false,
      registrationDate: new Date(),
      lastLoginDate: null
    });

    // Generate verification token
    const token = generateEmailVerifyToken(user.email);

    // Send verification email
    try {
      await emailService.sendEmail({
        to: user.email,
        subject: "Verify your email address - ONetwork Forum",
        html: verifyEmailTemplate(user, token),
        text: `Welcome to ONetwork Forum! Please verify your email by visiting: ${process.env.REACT_APP_URL}/verify-email?token=${token}`
      }, req);

      res.status(201).json({
        message: `Account created successfully! A verification email has been sent to ${user.email}`,
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          username: user.username,
          isVerified: user.isVerified
        }
      });
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Don't fail registration if email fails
      res.status(201).json({
        message: "Account created successfully! However, we couldn't send the verification email. Please request a new verification email.",
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          username: user.username,
          isVerified: user.isVerified
        }
      });
    }

  } catch (err) {
    console.error("Registration error:", err);
    res.status(500).json({
      message: "An error occurred during registration. Please try again."
    });
  }
};

/**
 * Enhanced login with rate limiting and security features
 */
const login = async (req, res) => {
  try {
    // Rate limiting
    try {
      await loginLimiter.consume(req.ip);
    } catch (rateLimiterRes) {
      return res.status(429).json({
        message: "Too many login attempts. Please try again later.",
        retryAfter: Math.round(rateLimiterRes.msBeforeNext / 1000)
      });
    }

    const { email, password } = req.body;

    if (!email?.trim() || !password?.trim()) {
      return res.status(422).json({
        message: "Email and password are required!"
      });
    }

    // Find user and explicitly select password
    const user = await User.findOne({ 
      email: email.toLowerCase().trim() 
    }).select('+password');

    if (!user) {
      return res.status(400).json({
        message: "Invalid email or password!"
      });
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) {
      return res.status(400).json({
        message: "Invalid email or password!"
      });
    }

    // Check if email is verified
    if (!user.isVerified) {
      return res.status(400).json({
        message: "Please verify your email address before logging in!",
        needsVerification: true,
        email: user.email
      });
    }

    // Update last login date
    await User.findByIdAndUpdate(user._id, { 
      lastLoginDate: new Date() 
    });

    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user, process.env.REFRESH_TOKEN_EXPIRATION);

    // Set secure cookie for refresh token
    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    res.status(200).json({
      message: "Login successful!",
      token: accessToken,
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        isVerified: user.isVerified,
        profilePicture: user.profilePicture,
        lastLoginDate: user.lastLoginDate
      }
    });

  } catch (err) {
    console.error("Login error:", err);
    res.status(500).json({
      message: "An error occurred during login. Please try again."
    });
  }
};

/**
 * Enhanced email verification
 */
const emailVerify = async (req, res) => {
  try {
    const { email } = req.user; // From token validation middleware

    if (!email) {
      return res.status(400).json({
        message: "Invalid verification token."
      });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({
        message: "User not found."
      });
    }

    if (user.isVerified) {
      return res.status(400).json({
        message: "Your email is already verified!"
      });
    }

    // Update user verification status
    await User.findByIdAndUpdate(user._id, { 
      isVerified: true,
      emailVerifiedDate: new Date()
    });

    res.status(200).json({
      message: "Email verified successfully! You can now log in to your account.",
      user: {
        id: user._id,
        email: user.email,
        isVerified: true
      }
    });

  } catch (err) {
    console.error("Email verification error:", err);
    res.status(500).json({
      message: "An error occurred during email verification."
    });
  }
};

/**
 * Enhanced send email verification
 */
const sendEmailVerification = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email?.trim()) {
      return res.status(400).json({
        message: "Email address is required!"
      });
    }

    // Validate email format
    try {
      await emailService.validateEmail(email);
    } catch (error) {
      return res.status(422).json({
        message: error.message
      });
    }

    const user = await User.findOne({ email: email.toLowerCase().trim() });
    if (!user) {
      return res.status(404).json({
        message: "No account found with this email address."
      });
    }

    if (user.isVerified) {
      return res.status(400).json({
        message: "Your email is already verified!"
      });
    }

    // Generate new verification token
    const token = generateEmailVerifyToken(user.email);

    // Send verification email
    await emailService.sendEmail({
      to: user.email,
      subject: "Verify your email address - ONetwork Forum",
      html: verifyEmailTemplate(user, token),
      text: `Please verify your email by visiting: ${process.env.REACT_APP_URL}/verify-email?token=${token}`
    }, req);

    res.status(200).json({
      message: `Verification email sent to ${user.email}`
    });

  } catch (err) {
    console.error("Send email verification error:", err);
    res.status(500).json({
      message: "Failed to send verification email. Please try again."
    });
  }
};

/**
 * Enhanced forgot password with rate limiting
 */
const sendForgotPassword = async (req, res) => {
  try {
    // Rate limiting
    try {
      await passwordResetLimiter.consume(req.body.email || req.ip);
    } catch (rateLimiterRes) {
      return res.status(429).json({
        message: "Too many password reset attempts. Please try again later.",
        retryAfter: Math.round(rateLimiterRes.msBeforeNext / 1000)
      });
    }

    const { email } = req.body;

    if (!email?.trim()) {
      return res.status(400).json({
        message: "Email address is required!"
      });
    }

    // Validate email format
    try {
      await emailService.validateEmail(email);
    } catch (error) {
      return res.status(422).json({
        message: error.message
      });
    }

    const user = await User.findOne({ email: email.toLowerCase().trim() });

    // Always return success to prevent email enumeration
    const successMessage = `If an account with ${email} exists, a password reset link has been sent.`;

    if (!user) {
      return res.status(200).json({
        message: successMessage
      });
    }

    // Generate password reset token
    const token = generatePasswordResetToken(user.email);

    // Send password reset email
    await emailService.sendEmail({
      to: user.email,
      subject: "Reset your password - ONetwork Forum",
      html: resetPasswordTemplate(user, token),
      text: `Reset your password by visiting: ${process.env.REACT_APP_URL}/reset-password?token=${token}`
    }, req);

    res.status(200).json({
      message: successMessage
    });

  } catch (err) {
    console.error("Send forgot password error:", err);
    res.status(500).json({
      message: "An error occurred. Please try again."
    });
  }
};

/**
 * Enhanced password reset
 */
const resetPassword = async (req, res) => {
  try {
    const { email } = req.user; // From token validation middleware
    const { newPassword, confirmNewPassword } = req.body;

    if (!email) {
      return res.status(400).json({
        message: "Invalid password reset token."
      });
    }

    if (!newPassword?.trim() || !confirmNewPassword?.trim()) {
      return res.status(400).json({
        message: "Both password fields are required!"
      });
    }

    if (newPassword !== confirmNewPassword) {
      return res.status(400).json({
        message: "Passwords do not match!"
      });
    }

    // Password strength validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(newPassword)) {
      return res.status(422).json({
        message: "Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character."
      });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({
        message: "User not found."
      });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password and add timestamp
    await User.findByIdAndUpdate(user._id, {
      password: hashedPassword,
      passwordResetDate: new Date()
    });

    res.status(200).json({
      message: "Password reset successfully! You can now log in with your new password."
    });

  } catch (err) {
    console.error("Reset password error:", err);
    res.status(500).json({
      message: "An error occurred while resetting your password."
    });
  }
};

module.exports = {
  register,
  login,
  emailVerify,
  sendEmailVerification,
  sendForgotPassword,
  resetPassword
};
