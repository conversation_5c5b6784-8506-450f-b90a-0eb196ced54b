const MaskAvatar = () => {
    return (
        <svg className="avatar-mask" preserveAspectRatio="xMinYMin meet" viewBox="0 0 36 36">
            <path d="M19.9,0.4l2,1.2C23,2.2,24.4,2,25.6,2a3.3,3.3,0,0,1,2.9,1.8l1,1.8a2.7,2.7,0,0,0,1,1l1.8,1A3.3,3.3,0,0,1,34,10.5c0,1.2-.2,2.7.4,3.7l1.2,2a3.4,3.4,0,0,1,.4,1.4V0H18.5A3.4,3.4,0,0,1,19.9.4Z"></path>
            <path d="M35.6,19.5l-1.2,2c-0.6,1-.4,2.5-0.4,3.7a3.3,3.3,0,0,1-1.6,2.9l-2,1.2a2.7,2.7,0,0,0-1,1l-1.1,2A3.3,3.3,0,0,1,25.4,34c-1.1,0-2.6-.2-3.6.4l-2,1.2a3,3,0,0,1-1.3.4H36V18.1A3.4,3.4,0,0,1,35.6,19.5Z"></path>
            <path d="M0.4,16.1l1.2-2c0.6-1,.4-2.5.4-3.6A3.3,3.3,0,0,1,3.7,7.7l2-1.1a2.7,2.7,0,0,0,1-1l1.2-2A3.3,3.3,0,0,1,10.8,2c1.2,0,2.7.2,3.8-.4l2-1.2A3.4,3.4,0,0,1,18,0H0V17.5A3.4,3.4,0,0,1,.4,16.1Z"></path>
            <path d="M14.5,34.4a2.7,2.7,0,0,0-1.4-.4H10.8A3.3,3.3,0,0,1,8,32.4a13,13,0,0,0-1.7-2.7A12.7,12.7,0,0,0,3.6,28,3.3,3.3,0,0,1,2,25.1c0-1.2.2-2.7-.4-3.7l-1.2-2A3.4,3.4,0,0,1,0,18V36H18.2A8.1,8.1,0,0,1,14.5,34.4Z"></path>
        </svg>
    );
}

export default MaskAvatar;