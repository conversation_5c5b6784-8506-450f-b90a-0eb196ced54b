# Modern Email Authentication System Setup Guide

## Overview

This guide will help you upgrade your ONetwork Forum email authentication system to a modern, secure, and scalable solution with the following features:

- **Multiple Email Providers**: Support for SendGrid, Mailgun, AWS SES, and SMTP
- **Enhanced Security**: Rate limiting, improved token management, and validation
- **Modern Templates**: Responsive, accessible email templates
- **Better UX**: Improved error handling and user feedback
- **Scalability**: Email service abstraction and fallback mechanisms

## Prerequisites

- Node.js 16+ installed
- MongoDB database
- Email service provider account (recommended: SendGrid for production)

## Installation Steps

### 1. Install Required Dependencies

Navigate to your server directory and install the new dependencies:

```bash
cd server
npm install @sendgrid/mail aws-sdk mailgun-js rate-limiter-flexible express-validator
```

### 2. Environment Configuration

Update your `.env` file with the new configuration options:

```env
# Email Service Configuration
EMAIL_PROVIDER=smtp  # Options: 'sendgrid', 'mailgun', 'aws_ses', 'smtp'
EMAIL_FALLBACK_PROVIDER=smtp

# App Configuration
APP_NAME=ONetwork Forum
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# SMTP Setup (Current/Fallback)
SMTP_SERVER=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_password

# SendGrid Configuration (Recommended for production)
# SENDGRID_API_KEY=your_sendgrid_api_key_here

# Mailgun Configuration
# MAILGUN_API_KEY=your_mailgun_api_key_here
# MAILGUN_DOMAIN=your_mailgun_domain_here

# AWS SES Configuration
# AWS_ACCESS_KEY_ID=your_aws_access_key_here
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
# AWS_REGION=us-east-1

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_ATTEMPTS=5
EMAIL_RATE_LIMIT_PER_HOUR=10
```

### 3. Update Your Main Server File

Update your `server.js` or main application file to use the new routes:

```javascript
// Add this to your server.js
const modernAuthRoutes = require('./routes/modernAuthRoutes');

// Replace or add alongside existing auth routes
app.use('/api/auth', modernAuthRoutes);

// Optional: Keep old routes for backward compatibility
// app.use('/api/auth/legacy', authRoutes);
```

### 4. Database Migration (Optional)

The enhanced user model includes new fields. Existing users will automatically get default values, but you may want to run a migration:

```javascript
// Optional migration script (create as migrate.js)
const User = require('./models/userModel');

async function migrateUsers() {
  await User.updateMany(
    { registrationDate: { $exists: false } },
    { 
      $set: { 
        registrationDate: new Date(),
        emailNotifications: true,
        marketingEmails: false,
        twoFactorEnabled: false
      }
    }
  );
  console.log('User migration completed');
}

// Run: node migrate.js
```

## Email Provider Setup

### Option 1: SendGrid (Recommended for Production)

1. Sign up at [SendGrid](https://sendgrid.com/)
2. Create an API key with "Mail Send" permissions
3. Add to your `.env`:
   ```env
   EMAIL_PROVIDER=sendgrid
   SENDGRID_API_KEY=your_api_key_here
   ```

### Option 2: Mailgun

1. Sign up at [Mailgun](https://www.mailgun.com/)
2. Get your API key and domain
3. Add to your `.env`:
   ```env
   EMAIL_PROVIDER=mailgun
   MAILGUN_API_KEY=your_api_key_here
   MAILGUN_DOMAIN=your_domain_here
   ```

### Option 3: AWS SES

1. Set up AWS SES in your AWS console
2. Create IAM credentials with SES permissions
3. Add to your `.env`:
   ```env
   EMAIL_PROVIDER=aws_ses
   AWS_ACCESS_KEY_ID=your_access_key
   AWS_SECRET_ACCESS_KEY=your_secret_key
   AWS_REGION=us-east-1
   ```

### Option 4: Keep Current SMTP

Your current SMTP setup will continue to work as a fallback.

## Frontend Integration

### Update API Calls

The new authentication endpoints provide better error handling and validation:

```javascript
// Registration with enhanced validation
const register = async (userData) => {
  try {
    const response = await axios.post('/api/auth/register', userData);
    return response.data;
  } catch (error) {
    // Enhanced error handling
    if (error.response?.data?.errors) {
      // Validation errors array
      console.log('Validation errors:', error.response.data.errors);
    }
    throw error;
  }
};

// Login with rate limiting awareness
const login = async (credentials) => {
  try {
    const response = await axios.post('/api/auth/login', credentials);
    return response.data;
  } catch (error) {
    if (error.response?.status === 429) {
      // Rate limited
      const retryAfter = error.response.data.retryAfter;
      console.log(`Rate limited. Retry after ${retryAfter} seconds`);
    }
    throw error;
  }
};
```

### Enhanced Email Verification Flow

```javascript
// Support both body and query parameters for tokens
const verifyEmail = async (token) => {
  try {
    const response = await axios.post('/api/auth/verify-email', { token });
    return response.data;
  } catch (error) {
    const errorCode = error.response?.data?.code;
    switch (errorCode) {
      case 'TOKEN_EXPIRED':
        // Show option to resend verification email
        break;
      case 'ALREADY_VERIFIED':
        // Redirect to login
        break;
      default:
        // Handle other errors
    }
    throw error;
  }
};
```

## Testing

### 1. Test Email Service Health

```bash
curl http://localhost:5000/api/auth/email-health
```

### 2. Test Registration Flow

```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "TestPass123!",
    "confirmPassword": "TestPass123!"
  }'
```

### 3. Test Rate Limiting

Make multiple rapid requests to see rate limiting in action.

## Security Features

### Rate Limiting
- **Login**: 5 attempts per 15 minutes per IP
- **Registration**: 3 registrations per hour per IP
- **Password Reset**: 3 attempts per hour per email/IP
- **Email Sending**: 10 emails per hour per IP

### Enhanced Validation
- Strong password requirements
- Email format and domain validation
- Disposable email detection
- Input sanitization and validation

### Token Security
- Improved error handling
- Better token expiration management
- Support for both body and query parameters

## Monitoring and Maintenance

### Health Checks
- Email service connectivity: `/api/auth/email-health`
- Monitor rate limiting metrics
- Track email delivery rates

### Logs
- Enhanced error logging with context
- Email sending success/failure tracking
- Authentication attempt monitoring

## Migration from Old System

1. **Gradual Migration**: Keep both old and new routes active
2. **User Communication**: Notify users about improved security
3. **Data Validation**: Ensure all existing users work with new system
4. **Monitoring**: Watch for any issues during transition

## Troubleshooting

### Common Issues

1. **Email not sending**: Check email service configuration and API keys
2. **Rate limiting too strict**: Adjust limits in `.env` file
3. **Token validation failing**: Verify JWT secret keys are set correctly
4. **SMTP connection issues**: Check firewall and SMTP server settings

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
DEBUG=email:*
```

## Next Steps

1. Set up monitoring and alerting
2. Implement email analytics
3. Add two-factor authentication
4. Consider implementing magic links
5. Set up email templates customization interface

For support, contact the development team or check the project documentation.
