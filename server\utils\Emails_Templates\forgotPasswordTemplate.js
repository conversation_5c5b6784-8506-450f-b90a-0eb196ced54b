module.exports = (user, token) => `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
    <head>
        <!--[if gte mso 9]>
            <xml>
                <o:OfficeDocumentSettings>
                    <o:AllowPNG />
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
            </xml>
        <![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="x-apple-disable-message-reformatting" />
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <!--<![endif]-->
        <title></title>

        <style type="text/css">
            * {
                font-family: "roboto", sans-serif !important;
            }
            ,
            @media only screen and (min-width: 620px) {
                .u-row {
                    width: 600px !important;
                }
                .u-row .u-col {
                    vertical-align: top;
                }

                .u-row .u-col-100 {
                    width: 600px !important;
                }
            }

            @media (max-width: 620px) {
                .u-row-container {
                    max-width: 100% !important;
                    padding-left: 0px !important;
                    padding-right: 0px !important;
                }
                .u-row .u-col {
                    min-width: 320px !important;
                    max-width: 100% !important;
                    display: block !important;
                }
                .u-row {
                    width: 100% !important;
                }
                .u-col {
                    width: 100% !important;
                }
                .u-col > div {
                    margin: 0 auto;
                }
            }
            body {
                margin: 0;
                padding: 0;
            }

            table,
            tr,
            td {
                vertical-align: top;
                border-collapse: collapse;
            }

            p {
                margin: 0;
            }

            .ie-container table,
            .mso-container table {
                table-layout: fixed;
            }

            * {
                line-height: inherit;
            }

            a[x-apple-data-detectors="true"] {
                color: inherit !important;
                text-decoration: none !important;
            }

            table,
            td {
                color: #000000;
            }
            #u_body a {
                color: #546cfb;
                text-decoration: underline;
            }
            @media (max-width: 480px) {
                #u_content_image_1 .v-src-width {
                    width: auto !important;
                }
                #u_content_image_1 .v-src-max-width {
                    max-width: 40% !important;
                }
                #u_content_heading_1 .v-font-size {
                    font-size: 38px !important;
                }
                #u_content_image_3 .v-container-padding-padding {
                    padding: 15px 20px 40px !important;
                }
                #u_content_image_3 .v-src-width {
                    width: 100% !important;
                }
                #u_content_image_3 .v-src-max-width {
                    max-width: 100% !important;
                }
                #u_content_text_5 .v-container-padding-padding {
                    padding: 10px 30px 11px 10px !important;
                }
                #u_content_text_14 .v-container-padding-padding {
                    padding: 10px !important;
                }
                #u_content_text_17 .v-container-padding-padding {
                    padding: 10px 30px 11px 10px !important;
                }
            }
        </style>

        <!--[if !mso]><!-->
        <link href="https://fonts.googleapis.com/css?family=roboto" rel="stylesheet" type="text/css" />
        <!--<![endif]-->
    </head>

    <body class="clean-body u_body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #546cfb; color: #000000;">
        <!--[if IE]><div class="ie-container"><![endif]-->
        <!--[if mso]><div class="mso-container"><![endif]-->
        <table
            id="u_body"
            style="border-collapse: collapse; table-layout: fixed; border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; vertical-align: top; min-width: 320px; margin: 0 auto; background-color: #546cfb; width: 100%;"
            cellpadding="0"
            cellspacing="0"
        >
            <tbody>
                <tr style="vertical-align: top;">
                    <td style="word-break: break-word; border-collapse: collapse !important; vertical-align: top;">
                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #546cfb;"><![endif]-->

                    

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #fbfcff;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fbfcff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table id="u_content_heading_1" style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 50px 10px 30px; font-family: roboto;" align="left">
                                                                <h1
                                                                    class="v-font-size"
                                                                    style="margin: 0px; color: #546cfb; line-height: 140%; text-align: center; word-wrap: break-word; font-weight: normal; font-family: roboto; font-size: 36px;"
                                                                >
                                                                    <strong>Password Reset</strong>
                                                                </h1>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table id="u_content_image_3" style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 15px 10px 40px; font-family: roboto;" align="left">
                                                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                                    <tr>
                                                                        <td style="padding-right: 0px; padding-left: 0px;" align="center">
                                                                            <img
                                                                                align="center"
                                                                                border="0"
                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674223597/email_assets/image-9_ib8m5y.jpg"
                                                                                alt="Hero Image"
                                                                                title="Hero Image"
                                                                                style="
                                                                                    outline: none;
                                                                                    text-decoration: none;
                                                                                    -ms-interpolation-mode: bicubic;
                                                                                    clear: both;
                                                                                    display: inline-block !important;
                                                                                    border: none;
                                                                                    height: auto;
                                                                                    float: none;
                                                                                    width: 55%;
                                                                                    max-width: 319px;
                                                                                "
                                                                                width="319"
                                                                                class="v-src-width v-src-max-width"
                                                                            />
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #ffffff;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table id="u_content_text_5" style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 40px 30px 20px 40px; font-family: roboto;" align="left">
                                                                <div style="color: #4b4a4a; line-height: 190%; text-align: left; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        <span style="font-size: 18px; line-height: 34.2px;">
                                                                            <strong><span style="line-height: 34.2px; font-size: 18px;">Dear ${user.firstName},</span></strong>
                                                                        </span>
                                                                    </p>
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        <span style="font-size: 16px; line-height: 30.4px;">You're receiving this message because you recently attempted to reset your password, Please click below to choose a new password.</span>
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <!--[if mso]>
                                                                    <style>
                                                                        .v-button {
                                                                            background: transparent !important;
                                                                        }
                                                                    </style>
                                                                <![endif]-->
                                                                <div align="center">
                                                                    <!--[if mso]><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${process.env.REACT_APP_URL}/reset-password?token=${token}" style="height:39px; v-text-anchor:middle; width:197px;" arcsize="10.5%"  stroke="f" fillcolor="#546cfb"><w:anchorlock/><center style="color:#FFFFFF;font-family:roboto;"><![endif]-->
                                                                    <a
                                                                        href="${process.env.REACT_APP_URL}/reset-password?token=${token}"
                                                                        target="_blank"
                                                                        class="v-button"
                                                                        style="
                                                                            box-sizing: border-box;
                                                                            display: inline-block;
                                                                            font-family: roboto;
                                                                            text-decoration: none;
                                                                            -webkit-text-size-adjust: none;
                                                                            text-align: center;
                                                                            color: #ffffff;
                                                                            background-color: #546cfb;
                                                                            border-radius: 4px;
                                                                            -webkit-border-radius: 4px;
                                                                            -moz-border-radius: 4px;
                                                                            width: auto;
                                                                            max-width: 100%;
                                                                            overflow-wrap: break-word;
                                                                            word-break: break-word;
                                                                            word-wrap: break-word;
                                                                            mso-border-alt: none;
                                                                        "
                                                                    >
                                                                        <span style="display: block; padding: 10px 20px; line-height: 120%;">
                                                                            <span style="font-size: 16px; line-height: 19.2px; font-family: roboto;">
                                                                                <strong><span style="line-height: 19.2px; font-size: 16px;">Reset Your Password</span></strong>
                                                                            </span>
                                                                        </span>
                                                                    </a>
                                                                    <!--[if mso]></center></v:roundrect><![endif]-->
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table id="u_content_text_14" style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px 30px 10px 40px; font-family: roboto;" align="left">
                                                                <div style="line-height: 190%; text-align: left; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        <span style="font-size: 16px; line-height: 30.4px;">If that button doesn't work, please copy and paste the following link in your browser:</span>
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <div style="color: #546cfb; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 140%; text-align: center;">
                                                                        <span style="text-decoration: underline; font-size: 14px; line-height: 19.6px;">
                                                                            <span style="font-size: 16px; line-height: 22.4px;">
                                                                                <strong><span style="line-height: 22.4px; font-size: 16px;">${process.env.REACT_APP_URL}/reset-password?token=${token}</span></strong>
                                                                            </span>
                                                                        </span>
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table id="u_content_text_17" style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 40px 30px 20px 40px; font-family: roboto;" align="left">
                                                                <div style="color: #4b4a4a; line-height: 190%; text-align: left; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        <span style="font-size: 18px; line-height: 34.2px;"><span style="line-height: 34.2px; font-size: 18px;">Cheers,</span></span>
                                                                    </p>
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        <span style="font-size: 18px; line-height: 34.2px;">
                                                                            <strong><span style="line-height: 34.2px; font-size: 18px;">ONetwork Owner.</span></strong>
                                                                        </span>
                                                                        <span style="font-size: 16px; line-height: 30.4px;"></span>
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 0px 0px 20px; font-family: roboto;" align="left">
                                                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                                    <tr>
                                                                        <td style="padding-right: 0px; padding-left: 0px;" align="center">
                                                                            <img
                                                                                align="center"
                                                                                border="0"
                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151971/email_assets/image-4_sbtq7u.png"
                                                                                alt="border"
                                                                                title="border"
                                                                                style="
                                                                                    outline: none;
                                                                                    text-decoration: none;
                                                                                    -ms-interpolation-mode: bicubic;
                                                                                    clear: both;
                                                                                    display: inline-block !important;
                                                                                    border: none;
                                                                                    height: auto;
                                                                                    float: none;
                                                                                    width: 100%;
                                                                                    max-width: 600px;
                                                                                "
                                                                                width="600"
                                                                                class="v-src-width v-src-max-width"
                                                                            />
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #fbfcff;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fbfcff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 50px 10px 10px; font-family: roboto;" align="left">
                                                                <h1 class="v-font-size" style="margin: 0px; line-height: 140%; text-align: center; word-wrap: break-word; font-weight: normal; font-size: 26px;">
                                                                    <strong>Get In Touch</strong>
                                                                </h1>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <table
                                                                    height="0px"
                                                                    align="center"
                                                                    border="0"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    width="100%"
                                                                    style="
                                                                        border-collapse: collapse;
                                                                        table-layout: fixed;
                                                                        border-spacing: 0;
                                                                        mso-table-lspace: 0pt;
                                                                        mso-table-rspace: 0pt;
                                                                        vertical-align: top;
                                                                        border-top: 1px solid #bbbbbb;
                                                                        -ms-text-size-adjust: 100%;
                                                                        -webkit-text-size-adjust: 100%;
                                                                    "
                                                                >
                                                                    <tbody>
                                                                        <tr style="vertical-align: top;">
                                                                            <td
                                                                                style="
                                                                                    word-break: break-word;
                                                                                    border-collapse: collapse !important;
                                                                                    vertical-align: top;
                                                                                    font-size: 0px;
                                                                                    line-height: 0px;
                                                                                    mso-line-height-rule: exactly;
                                                                                    -ms-text-size-adjust: 100%;
                                                                                    -webkit-text-size-adjust: 100%;
                                                                                "
                                                                            >
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 20px 10px; font-family: roboto;" align="left">
                                                                <div align="center">
                                                                    <div style="display: table; max-width: 191px;">
                                                                        <!--[if (mso)|(IE)]><table width="191" cellpadding="0" cellspacing="0" border="0"><tr><td style="border-collapse:collapse;" align="center"><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse:collapse; mso-table-lspace: 0pt;mso-table-rspace: 0pt; width:191px;"><tr><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 16px;" valign="top"><![endif]-->
                                                                        <table
                                                                            align="left"
                                                                            border="0"
                                                                            cellspacing="0"
                                                                            cellpadding="0"
                                                                            width="32"
                                                                            height="32"
                                                                            style="
                                                                                width: 32px !important;
                                                                                height: 32px !important;
                                                                                display: inline-block;
                                                                                border-collapse: collapse;
                                                                                table-layout: fixed;
                                                                                border-spacing: 0;
                                                                                mso-table-lspace: 0pt;
                                                                                mso-table-rspace: 0pt;
                                                                                vertical-align: top;
                                                                                margin-right: 16px;
                                                                            "
                                                                        >
                                                                            <tbody>
                                                                                <tr style="vertical-align: top;">
                                                                                    <td align="left" valign="middle" style="word-break: break-word; border-collapse: collapse !important; vertical-align: top;">
                                                                                        <a href="https://twitter.com/Sphinxo_Dz" title="Twitter" target="_blank">
                                                                                            <img
                                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151972/email_assets/image-1_l7g8jl.png"
                                                                                                alt="Twitter"
                                                                                                title="Twitter"
                                                                                                width="32"
                                                                                                style="
                                                                                                    outline: none;
                                                                                                    text-decoration: none;
                                                                                                    -ms-interpolation-mode: bicubic;
                                                                                                    clear: both;
                                                                                                    display: block !important;
                                                                                                    border: none;
                                                                                                    height: auto;
                                                                                                    float: none;
                                                                                                    max-width: 32px !important;
                                                                                                "
                                                                                            />
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 16px;" valign="top"><![endif]-->
                                                                        <table
                                                                            align="left"
                                                                            border="0"
                                                                            cellspacing="0"
                                                                            cellpadding="0"
                                                                            width="32"
                                                                            height="32"
                                                                            style="
                                                                                width: 32px !important;
                                                                                height: 32px !important;
                                                                                display: inline-block;
                                                                                border-collapse: collapse;
                                                                                table-layout: fixed;
                                                                                border-spacing: 0;
                                                                                mso-table-lspace: 0pt;
                                                                                mso-table-rspace: 0pt;
                                                                                vertical-align: top;
                                                                                margin-right: 16px;
                                                                            "
                                                                        >
                                                                            <tbody>
                                                                                <tr style="vertical-align: top;">
                                                                                    <td align="left" valign="middle" style="word-break: break-word; border-collapse: collapse !important; vertical-align: top;">
                                                                                        <a href="https://instagram.com/ilyas_belfar" title="Instagram" target="_blank">
                                                                                            <img
                                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151971/email_assets/image-3_zredhm.png"
                                                                                                alt="Instagram"
                                                                                                title="Instagram"
                                                                                                width="32"
                                                                                                style="
                                                                                                    outline: none;
                                                                                                    text-decoration: none;
                                                                                                    -ms-interpolation-mode: bicubic;
                                                                                                    clear: both;
                                                                                                    display: block !important;
                                                                                                    border: none;
                                                                                                    height: auto;
                                                                                                    float: none;
                                                                                                    max-width: 32px !important;
                                                                                                "
                                                                                            />
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 16px;" valign="top"><![endif]-->
                                                                        <table
                                                                            align="left"
                                                                            border="0"
                                                                            cellspacing="0"
                                                                            cellpadding="0"
                                                                            width="32"
                                                                            height="32"
                                                                            style="
                                                                                width: 32px !important;
                                                                                height: 32px !important;
                                                                                display: inline-block;
                                                                                border-collapse: collapse;
                                                                                table-layout: fixed;
                                                                                border-spacing: 0;
                                                                                mso-table-lspace: 0pt;
                                                                                mso-table-rspace: 0pt;
                                                                                vertical-align: top;
                                                                                margin-right: 16px;
                                                                            "
                                                                        >
                                                                            <tbody>
                                                                                <tr style="vertical-align: top;">
                                                                                    <td align="left" valign="middle" style="word-break: break-word; border-collapse: collapse !important; vertical-align: top;">
                                                                                        <a href="https://facebook.com/sphinxo.dz" title="Facebook" target="_blank">
                                                                                            <img
                                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151972/email_assets/image-2_gnu1ek.png"
                                                                                                alt="Facebook"
                                                                                                title="Facebook"
                                                                                                width="32"
                                                                                                style="
                                                                                                    outline: none;
                                                                                                    text-decoration: none;
                                                                                                    -ms-interpolation-mode: bicubic;
                                                                                                    clear: both;
                                                                                                    display: block !important;
                                                                                                    border: none;
                                                                                                    height: auto;
                                                                                                    float: none;
                                                                                                    max-width: 32px !important;
                                                                                                "
                                                                                            />
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 0px;" valign="top"><![endif]-->
                                                                        <table
                                                                            align="left"
                                                                            border="0"
                                                                            cellspacing="0"
                                                                            cellpadding="0"
                                                                            width="32"
                                                                            height="32"
                                                                            style="
                                                                                width: 32px !important;
                                                                                height: 32px !important;
                                                                                display: inline-block;
                                                                                border-collapse: collapse;
                                                                                table-layout: fixed;
                                                                                border-spacing: 0;
                                                                                mso-table-lspace: 0pt;
                                                                                mso-table-rspace: 0pt;
                                                                                vertical-align: top;
                                                                                margin-right: 0px;
                                                                            "
                                                                        >
                                                                            <tbody>
                                                                                <tr style="vertical-align: top;">
                                                                                    <td align="left" valign="middle" style="word-break: break-word; border-collapse: collapse !important; vertical-align: top;">
                                                                                        <a href="https://github.com/ilyasbelfar" title="GitHub" target="_blank">
                                                                                            <img
                                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151971/email_assets/image-6_pmqmr6.png"
                                                                                                alt="GitHub"
                                                                                                title="GitHub"
                                                                                                width="32"
                                                                                                style="
                                                                                                    outline: none;
                                                                                                    text-decoration: none;
                                                                                                    -ms-interpolation-mode: bicubic;
                                                                                                    clear: both;
                                                                                                    display: block !important;
                                                                                                    border: none;
                                                                                                    height: auto;
                                                                                                    float: none;
                                                                                                    max-width: 32px !important;
                                                                                                "
                                                                                            />
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <table
                                                                    height="0px"
                                                                    align="center"
                                                                    border="0"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    width="82%"
                                                                    style="
                                                                        border-collapse: collapse;
                                                                        table-layout: fixed;
                                                                        border-spacing: 0;
                                                                        mso-table-lspace: 0pt;
                                                                        mso-table-rspace: 0pt;
                                                                        vertical-align: top;
                                                                        border-top: 1px solid #bbbbbb;
                                                                        -ms-text-size-adjust: 100%;
                                                                        -webkit-text-size-adjust: 100%;
                                                                    "
                                                                >
                                                                    <tbody>
                                                                        <tr style="vertical-align: top;">
                                                                            <td
                                                                                style="
                                                                                    word-break: break-word;
                                                                                    border-collapse: collapse !important;
                                                                                    vertical-align: top;
                                                                                    font-size: 0px;
                                                                                    line-height: 0px;
                                                                                    mso-line-height-rule: exactly;
                                                                                    -ms-text-size-adjust: 100%;
                                                                                    -webkit-text-size-adjust: 100%;
                                                                                "
                                                                            >
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <div style="color: #908f8f; line-height: 190%; text-align: center; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 190%;">
                                                                        © 2023 ONetwork Forum. All Rights Reserved.
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 0px; font-family: roboto;" align="left">
                                                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                                    <tr>
                                                                        <td style="padding-right: 0px; padding-left: 0px;" align="center">
                                                                            <img
                                                                                align="center"
                                                                                border="0"
                                                                                src="https://res.cloudinary.com/djuxwysbl/image/upload/v1674151971/email_assets/image-5_un7alv.png"
                                                                                alt="border"
                                                                                title="border"
                                                                                style="
                                                                                    outline: none;
                                                                                    text-decoration: none;
                                                                                    -ms-interpolation-mode: bicubic;
                                                                                    clear: both;
                                                                                    display: inline-block !important;
                                                                                    border: none;
                                                                                    height: auto;
                                                                                    float: none;
                                                                                    width: 100%;
                                                                                    max-width: 600px;
                                                                                "
                                                                                width="600"
                                                                                class="v-src-width v-src-max-width"
                                                                            />
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="u-row-container" style="padding: 0px; background-color: transparent;">
                            <div class="u-row" style="margin: 0 auto; min-width: 320px; max-width: 600px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: transparent;">
                                <div style="border-collapse: collapse; display: table; width: 100%; height: 100%; background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px; min-width: 600px; display: table-cell; vertical-align: top;">
                                        <div style="height: 100%; width: 100% !important; border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!--><div
                                                style="
                                                    height: 100%;
                                                    padding: 0px;
                                                    border-top: 0px solid transparent;
                                                    border-left: 0px solid transparent;
                                                    border-right: 0px solid transparent;
                                                    border-bottom: 0px solid transparent;
                                                    border-radius: 0px;
                                                    -webkit-border-radius: 0px;
                                                    -moz-border-radius: 0px;
                                                "
                                            ><!--<![endif]-->
                                                <table style="font-family: roboto;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding" style="overflow-wrap: break-word; word-break: break-word; padding: 10px; font-family: roboto;" align="left">
                                                                <table
                                                                    height="0px"
                                                                    align="center"
                                                                    border="0"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    width="100%"
                                                                    style="
                                                                        border-collapse: collapse;
                                                                        table-layout: fixed;
                                                                        border-spacing: 0;
                                                                        mso-table-lspace: 0pt;
                                                                        mso-table-rspace: 0pt;
                                                                        vertical-align: top;
                                                                        border-top: 0px solid #bbbbbb;
                                                                        -ms-text-size-adjust: 100%;
                                                                        -webkit-text-size-adjust: 100%;
                                                                    "
                                                                >
                                                                    <tbody>
                                                                        <tr style="vertical-align: top;">
                                                                            <td
                                                                                style="
                                                                                    word-break: break-word;
                                                                                    border-collapse: collapse !important;
                                                                                    vertical-align: top;
                                                                                    font-size: 0px;
                                                                                    line-height: 0px;
                                                                                    mso-line-height-rule: exactly;
                                                                                    -ms-text-size-adjust: 100%;
                                                                                    -webkit-text-size-adjust: 100%;
                                                                                "
                                                                            >
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div>
                                            <!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>

                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
        <!--[if mso]></div><![endif]-->
        <!--[if IE]></div><![endif]-->
    </body>
</html>`;
