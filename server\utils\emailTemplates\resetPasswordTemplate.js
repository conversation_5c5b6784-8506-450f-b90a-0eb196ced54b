/**
 * Modern Password Reset Template
 * Responsive and accessible password reset email template
 */

const { getBaseTemplate } = require('./modernEmailTemplate');

const resetPasswordTemplate = (user, token, options = {}) => {
  const {
    appName = 'ONetwork Forum',
    appUrl = process.env.REACT_APP_URL || 'http://localhost:3001',
    primaryColor = '#dc3545' // Red color for password reset
  } = options;

  const resetUrl = `${appUrl}/reset-password?token=${token}`;
  const firstName = user.firstName || user.username || 'there';

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="${primaryColor}" stroke-width="2"/>
        <circle cx="12" cy="16" r="1" fill="${primaryColor}"/>
        <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="${primaryColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>

    <h2 style="color: #333; font-size: 24px; font-weight: 600; margin-bottom: 20px; text-align: center;">
      Reset Your Password
    </h2>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      Hi ${firstName},
    </p>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      We received a request to reset the password for your ${appName} account. If you made this request, 
      click the button below to create a new password.
    </p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="${resetUrl}" 
         class="button"
         style="display: inline-block; padding: 16px 32px; background-color: ${primaryColor}; color: #ffffff !important; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">
        Reset Password
      </a>
    </div>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      This password reset link will expire in <strong>20 minutes</strong> for security reasons.
    </p>

    <div class="security-notice" style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 16px; margin: 20px 0;">
      <p style="margin: 0; color: #721c24; font-size: 14px;">
        <strong>Security Notice:</strong> If you didn't request a password reset, please ignore this email. 
        Your password will remain unchanged. Consider changing your password if you suspect unauthorized access to your account.
      </p>
    </div>

    <p style="font-size: 14px; line-height: 1.6; margin-top: 30px; color: #6c757d;">
      If the button above doesn't work, you can copy and paste this link into your browser:
    </p>
    
    <p style="font-size: 14px; word-break: break-all; background-color: #f8f9fa; padding: 12px; border-radius: 4px; margin: 10px 0;">
      <a href="${resetUrl}" style="color: ${primaryColor};">${resetUrl}</a>
    </p>

    <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

    <h3 style="color: #333; font-size: 18px; font-weight: 600; margin-bottom: 15px;">
      Password Security Tips
    </h3>

    <ul style="padding-left: 20px; margin-bottom: 20px; color: #6c757d; font-size: 14px;">
      <li style="margin-bottom: 8px;">Use a unique password that you don't use for other accounts</li>
      <li style="margin-bottom: 8px;">Make it at least 8 characters long with a mix of letters, numbers, and symbols</li>
      <li style="margin-bottom: 8px;">Consider using a password manager to generate and store secure passwords</li>
      <li style="margin-bottom: 8px;">Enable two-factor authentication when available</li>
    </ul>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      If you continue to have problems, please contact our support team.
    </p>

    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      Best regards,<br>
      The ${appName} Team
    </p>
  `;

  const templateOptions = {
    title: `Reset your password - ${appName}`,
    preheader: `Reset your ${appName} password. This link expires in 20 minutes.`,
    primaryColor,
    appName,
    appUrl
  };

  return getBaseTemplate(content, templateOptions);
};

module.exports = resetPasswordTemplate;
